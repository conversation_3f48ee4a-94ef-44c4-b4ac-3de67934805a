from core.base.langchain_sender import LangchainBaseSender
import asyncio
import re
from typing import Dict, Any,AsyncIterator
import json
from core.config.app_logger import logger
from mcpserver.mcp_server import save_page_config,check_page_config
################################
# 动态页面智能体
################################
class PageCreatorSender(LangchainBaseSender):
    """动态页面创建专用发送器"""

    async def _handle_response(self, chunk: Dict[str, Any]) -> AsyncIterator[str]:
        """处理响应类型chunk并流式返回结果"""
        _content = chunk.get("content", "")
        json_match = re.search(r"```json[\r\n]+(.*?)[\r\n]+```", _content, re.DOTALL)
        
        if not json_match:
            return

        try:
            json_str = json_match.group(1).strip()
            config_json = json.loads(json_str)
            logger.debug(f"解析到配置JSON: {config_json}")

            # 第一阶段：配置验证
            validate_result =  check_page_config(kwargs={"config_json": config_json})
            result_data = json.loads(validate_result)
            
            tool_start = {"type": "tool_start", "tool": "参数验证"}
            yield self._handle_tool_start(tool_start)
            
            await asyncio.sleep(1.5)

            if not result_data.get("success"):
                yield f"验证错误: {result_data.get('resultMessage', '未知错误')}"
                return
            
            yield self._handle_tool_result({"tool": "参数验证", "result": "success"  })

            # 第二阶段：保存配置
            yield self._handle_tool_start({"type": "tool_start", "tool": "保存配置"})
            validated_config = result_data.get("config_json", {})
            await asyncio.sleep(0.5)
            logger.info(f"save_page_config：{validated_config}")
            save_result = await save_page_config(kwargs={"config_json": validated_config})
            object_reulst = json.loads(save_result)
            await asyncio.sleep(0.5)
            if object_reulst.get("success"):
                dynamicId = object_reulst["data"].get("dynamicId")
                dynamicName = object_reulst["data"].get("dynamicName")
                dynamic_data = {
                    "dynamicId": dynamicId,
                }
                self.transmitter._set_data_obj(dynamic_data)
                yield self._handle_tool_result({"tool": "保存配置", "result": "success"})
                async for char in self._generate_content_stream("操作完成，请点击下方预览按钮查看效果~"
                ):
                    yield char
                yield self._generate_preview_widget(dynamicId, dynamicName)
            else:
                logger.error(f"配置保存异常: {save_result}")
                err_msg = object_reulst.get('errorMessage', '未知错误')
                yield self._handle_tool_result({"tool": "保存配置", "result": "faild","tool_msg": err_msg })
                yield "配置保存异常,请稍后重试！"

        except json.JSONDecodeError as e:
            yield f"JSON解析错误: {str(e)}"
        except Exception as e:
            logger.error(f"处理流程异常: {str(e)}")
            yield self._handle_tool_result({"tool": "保存配置", "result": "faild"})
            yield f"处理异常: {str(e)}"
   