import traceback
from typing import Dict,Any
from uuid import uuid4
from fastapi import APIR<PERSON><PERSON>, Depends, Body, Request
from fastapi.responses import StreamingResponse
from pydantic import BaseModel
from core.config.security import check_user
from core.vo.user_vo import UserVO
from core.message.transmitter import Transmitter
from core.message.creator import create_human_message
from core.services.database import db_manager
from core.services.database.crud.message import message_curd
from core.services.database.crud.conversation import conversation_curd
from core.services.database.schemas.conversation import ConversationCreate
from core.factory.agent_factory import AgentFactory
from core.base.langchain_sender import LangchainBaseSender
from core.base.dify_sender import DifyBaseSender
from core.api.nl2sql_sender import HttpBase<PERSON>ender
from core.config.app_logger import logger

###################
# 大模型调用统一入口
###################
router = APIRouter(tags=["Chat"], prefix="/chat")

class CompletionReqBody(BaseModel):
    message: str
    conversation_id: str | None = None
    agent_code: str | None = None
    extendParams: Dict[str, Any] | None = None  

@router.post("/completion")
async def query_llm(
    request: Request,
    body: CompletionReqBody = Body(..., description="用户提问内容"),
    user: UserVO = Depends(check_user),
):
    message = body.message
    conversation_id = body.conversation_id
    agent_code = body.agent_code
    extendParams = body.extendParams
    logger.info(f"extendParams: {extendParams}")
    if not conversation_id:
        # 新建会话
        async with db_manager.session() as session:
            new_conversation = ConversationCreate(
                user_id=user.userId,
                title=message[:20],
                current_agent_code=agent_code,
            )
            await conversation_curd.create(db=session, obj_input=new_conversation)
            conversation_id = new_conversation.id

    # 保存用户消息
    async with db_manager.session() as session:
        new_message = create_human_message(message, conversation_id)
        await message_curd.create(db=session, obj_input=new_message)

    try:
        transmitter = Transmitter(conversation_id=conversation_id, message_id=uuid4().hex, agent_code=agent_code)
        senter = AgentFactory.get_sender(agent_code)

        if isinstance(senter, (LangchainBaseSender, DifyBaseSender,HttpBaseSender)):
            stream = senter.generate_stream(
                transmitter=transmitter,
                request=request,
                question=message,
                user_id=user.userId,
                conversation_id=conversation_id,
                extend_params=extendParams
            )
            return StreamingResponse(
                stream,
                media_type="text/event-stream",
                headers={
                    "Cache-Control": "no-cache",
                    "Connection": "keep-alive",
                },
            ) 
        else:
            return StreamingResponse(
                    senter(transmitter, request, message, user.userId, agent_code, conversation_id),
                    media_type="text/event-stream",
                    headers={
                        "Cache-Control": "no-cache",
                        "Connection": "keep-alive",
                    },
                )    
    except Exception as e:
        # 打印完整错误堆栈到控制台（用于调试）
        traceback.print_exc()

        # 返回给前端的错误信息包含：
        error_info = {
            "error_type": type(e).__name__,
            "error_message": str(e),
            "stack_trace": traceback.format_exc(),
        }
        return {
            "code": 500,
            "message": f"调用大模型失败: {type(e).__name__} - {str(e)}",
            "error": error_info, 
        }

    