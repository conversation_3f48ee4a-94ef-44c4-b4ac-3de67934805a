from datetime import datetime, timezone
from typing import Annotated

from fastapi import APIRouter, Depends, Body, HTTPException
from sqlalchemy import text

from core.vo.user_vo import UserVO
from core.services.database.schemas.conversation import (
    ConversationCreate,
    ConversationUpdate,
)
from core.config.security import check_user
from core.services.database import db_manager
from core.services.database.crud.conversation import conversation_curd

router = APIRouter(prefix="/conversation", tags=["Conversation"])


@router.get("")
async def query_conversation_by_user_id(
    user: UserVO = Depends(check_user),
    size: int = 10,
    page: int = 1,
    keyword: str = "",
):
    """
    根据用户ID查询会话列表

    Args:
        user: 当前用户信息
        size: 每页记录数
        page: 页码，从1开始

    Returns:
        会话列表
    """
    try:
        async with db_manager.session() as session:
            # 查询总数
            count_query = text(
                """
                SELECT COUNT(*) as count FROM conversation
                WHERE user_id = :user_id AND title LIKE :keyword
            """
            )

            # 替换通配符和特殊字符
            keyword = keyword.replace("\\", "\\\\").replace("%", "\\%").replace("_", "\\_")

            result = await session.execute(count_query, {"user_id": user.userId, "keyword": f"%{keyword}%"})
            total_row = result.mappings().one()
            total_count = total_row["count"] if total_row else 0

            # 查询会话列表
            conversation_query = text(
                """
                SELECT id, title, updated_at, current_agent_code
                FROM conversation
                WHERE user_id = :user_id AND title LIKE :keyword
                GROUP BY id
                ORDER BY updated_at DESC
                LIMIT :size OFFSET :offset
            """
            )
            conversations = await session.execute(
                conversation_query,
                {"user_id": user.userId, "keyword": f"%{keyword}%", "size": size, "offset": (page - 1) * size},
            )
            conversation_rows = conversations.mappings().all()

            pagination = {
                "total": total_count,
                "size": size,
                "page": page,
                "page_total": (total_count + size - 1) // size,  # 向上取整
            }

            return {
                "code": 200,
                "data": conversation_rows,
                "pagination": pagination,
                "message": "查询成功",
            }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"数据库查询错误: {str(e)}") from e


@router.post("/")
async def create_conversation(
    user: UserVO = Depends(check_user),
    conversation_name: str = Body(..., description="会话名称"),
):
    """
    新建会话

    Args:
        user: 当前用户信息
        conversation_name: 会话名称

    Returns:
        会话ID
    """
    try:
        async with db_manager.session() as session:
            # 创建新的会话记录
            new_conversation = ConversationCreate(
                user_id=user.userId,
                name=conversation_name,
                created_at=datetime.now(timezone.utc),
            )
            conversation_curd.create(db=session, obj_input=new_conversation)

            return {
                "code": 201,
                "message": "会话创建成功",
                "data": {"conversation_id": new_conversation["id"]},
            }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"创建会话失败: {str(e)}") from e


@router.put("/{conversation_id}")
async def update_conversation(
    conversation_id,
    data=Body(..., description="会话名称"),
):
    """
    更新会话

    Args:
        conversation_id: 会话ID
        data: 会话名称

    Returns:
        会话ID
    """
    try:
        async with db_manager.session() as session:
            # 更新会话记录
            conversation_list = await conversation_curd.get_by_conversation_id(
                session, _id=conversation_id
            )
            conversation = conversation_list[0]
            if not conversation:
                raise HTTPException(status_code=404, detail="会话不存在")

            # 更新会话，仅能修改名称
            new_conversation = ConversationUpdate(
                title=data["title"]
            )
            await conversation_curd.update(
                db=session, db_obj=conversation, obj_input=new_conversation
            )

            return {
                "code": 200,
                "message": "会话更新成功",
            }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"更新会话失败: {str(e)}") from e


@router.delete("/{conversation_id}")
async def delete_conversation(
    conversation_id,
):
    """
    删除会话

    Args:
        conversation_id: 会话ID

    Returns:
        会话ID
    """
    try:
        async with db_manager.session() as session:
            # 删除会话记录
            await conversation_curd.remove(db=session, _id=conversation_id)

            return {
                "code": 200,
                "message": "会话删除成功",
            }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"删除会话失败: {str(e)}") from e


# 通过id查询会话详情
@router.get("/{conversation_id}")
async def query_conversation_by_id(
    conversation_id,
):
    """
    通过id查询会话详情

    Args:
        conversation_id: 会话ID

    Returns:
        会话详情
    """
    try:
        async with db_manager.session() as session:
            # 查询会话详情
            conversation_list = await conversation_curd.get_by_conversation_id(
                session, _id=conversation_id
            )
            if len(conversation_list) == 0:
                raise HTTPException(status_code=404, detail="会话不存在")
            conversation = conversation_list[0]

            return {
                "code": 200,
                "data": conversation,
                "message": "查询成功",
            }
    except Exception as e:
        if isinstance(e, HTTPException):
            raise e
        raise HTTPException(status_code=500, detail=f"查询会话失败: {str(e)}") from e
