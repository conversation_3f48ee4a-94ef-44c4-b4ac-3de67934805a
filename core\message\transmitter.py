"""
消息发送器

消息块是构成消息包的基本单元，负责传输消息的具体内容。每个 chunk 包含一个消息块 ID 和一个数据块，数据块可以是纯文本、结构化数据或其他类型的数据。
chunk 的数量和顺序会影响消息的完整性和正确性，因此需要在解析时进行验证。

Package 是消息的最小解析单元，包含一个或多个 chunk；客户端在接收 chunk 时先将 chunk 存储到 Buffer 中，直到接收到完整的 package 后才开始解析，
解析时会验证 chunk 数量和顺序是否正确，确保消息的完整性和正确性。

package 的类型：text, structured，分别表示纯文本消息、结构化数据消息。
text 和 structured 类型的 package 包含一个消息体，text 和 structured 类型的 package 可以包含多个 chunk；

text 类型的 package 用于传输纯文本消息，默认使用 Markdown 格式文本便于解析展示，它的内容会实时更新，用户可以在接收过程中看到消息的逐步生成。

Text Package
text 类型的 package 用于传输纯文本消息，默认使用 Markdown 格式文本便于解析展示，它的内容会实时更新，用户可以在接收过程中看到消息的逐步生成。

Structured Package
结构化数据类型的 package 主要用于传输复杂的数据结构，它是一个 JSON 对象，它的内容是静态的，用户在接收完成后才能看到完整的消息。
"""

from datetime import datetime, timezone
import json
from typing import List

from core.services.database import db_manager
from core.message.types import Chunk, MessagePackage, MessageType, MessagePackageStatus
from core.message.structured_msg import HeaderMessage
from core.services.database.crud.message import MessageCreate, message_curd
from core.services.database.crud.conversation import conversation_curd

# event_type 枚举值
EVENT_TYPE_START = 1000
EVENT_TYPE_LOADING = 1001
EVENT_TYPE_END = 1002
EVENT_TYPE_ERROR = 2000
# package_type 枚举值
PACKAGE_TYPE_NONE = -1
PACKAGE_TYPE_TEXT = 0
PACKAGE_TYPE_STRUCTURED = 1
PACKAGE_TYPE_THINKING = 2
PACKAGE_TYPE_ERROR = 3
# structured_chunk_type 枚举值
STRUCTURED_CHUNK_TYPE_HEADER = "header"
STRUCTURED_CHUNK_TYPE_COMMAND = "command"


class Transmitter:
    """
    消息发送器
    """

    def __init__(self, conversation_id: str, message_id: str, agent_code: str = None):
        # 会话id
        self.conversation_id = conversation_id
        # 消息id
        self.message_id = message_id
        # 智能体id
        self.agent_code = agent_code
        # 数据存储属性
        self.data_object = None
        # 事件id
        self.event_id = -1
        # 事件类型 0: start, 1: loading, 2: end, 3: error
        self.event_type = EVENT_TYPE_START
        # 当前发送消息包id，从0开始
        self.package_id = -1
        # 当前发送消息包类型，0：text，1: structured
        self.package_type = None
        # 当前发送消息包内chunk id，从0开始
        self.chunk_id = -1
        # 当前发送消息包内chunk类型，0：text，1: structured
        self.chunks = []

    def start(self):
        """
        开始发送消息包
        """
        self.event_id = 0
        self.event_type = EVENT_TYPE_START
        self.package_id = 0
        self.chunk_id = 0
        self.package_type = PACKAGE_TYPE_STRUCTURED

        header_msg = HeaderMessage(
            conversation_id=self.conversation_id, message_id=self.message_id
        )

        # header_msg 转成 dict
        header_dict = header_msg.__dict__
        header_str = json.dumps(header_dict)
        chunk = self.create_chunk(header_str, is_last=True)
        return self.wrap_event(chunk)

    def send_message(
        self, data, *, package_type: int, is_last=False, is_new_package=False
    ):
        """
        发送消息包

        :param data: 消息内容
        :param package_type: 消息包类型
        :param is_last: 是否是最后一个消息包
        :param is_new_package: 是否是新的消息包
        :return: 消息包
        """
        # 如果是新的消息包，重置 event_id 和 chunk_id
        self.event_id += 1
        self.event_type = EVENT_TYPE_LOADING
        if package_type is not None:
            self.package_type = package_type

        if is_new_package is True:
            # 新消息包时重置包ID和块ID计数器
            self.package_id += 1
            self.chunk_id = 0
        else:
            # 同一消息包内，块ID递增
            self.chunk_id += 1

        # 创建并序列化消息块
        chunk = self.create_chunk(data, is_last)

        # 包装为SSE事件格式
        return self.wrap_event(chunk)

    def send_error(self, data: str):
        """
        发送错误消息包
        """
        self.event_id += 1
        self.event_type = EVENT_TYPE_ERROR
        self.package_id += 1
        self.chunk_id = 0
        self.package_type = PACKAGE_TYPE_ERROR

        chunk = self.create_chunk(data, is_last=True)
        return self.wrap_event(chunk)
    
    def _set_data_obj(self, data: dict):
        """设置自定义数据（私有方法）"""
        if not self.data_object:
            self.data_object = {}
        self.data_object.update(data)
    
    

    async def end(self):
        """
        结束消息流传输

        生成一个结束事件，标记消息传输完成，同时将所有收集的消息块保存到数据库。
        结束事件使用PACKAGE_TYPE_NONE类型，表示不再有新的消息包。

        :return: 格式化的SSE结束事件
        """
        # 递增事件ID并设置为结束类型
        self.event_id += 1
        self.event_type = EVENT_TYPE_END
        # 创建新的空包用于标记结束
        self.package_id += 1
        self.chunk_id = 0
        self.package_type = PACKAGE_TYPE_NONE

        # 创建一个空数据的最终块
        chunk = self.create_chunk(None, is_last=True)
        # 结束时将所有收集的消息包持久化到数据库
        await self.save_packages()
        return self.wrap_event(chunk)

    def create_chunk(self, data, is_last=False):
        """
        创建消息块

        根据当前的状态信息创建一个新的消息块，用于传输数据。

        :param data: 要传输的数据内容，可以是字符串或者结构化数据
        :param is_last: 布尔值，标记是否为当前消息包的最后一个块
        :return: 序列化为JSON字符串的消息块
        """
        # 创建包含元数据的消息块对象
        # TODO: 优化 Chunk 类型，与 MessagePackage 保持一致
        chunk = Chunk(
            event_id=self.event_id,  # 事件ID，用于客户端识别事件顺序
            event_type=self.event_type,  # 事件类型：开始、加载中、结束、错误
            package_id=self.package_id,  # 消息包ID，用于合并相关块
            package_type=self.package_type,  # 包类型：文本或结构化数据
            chunk_id=self.chunk_id,  # 块ID，确保正确的块顺序
            is_last=is_last,  # 标记是否为包中的最后一个块
            data=data,  # 实际传输的数据内容
        )

        # 将当前块添加到块历史记录中，用于后续合并和持久化
        self.chunks.append(chunk)
        # 返回序列化的JSON字符串
        return json.dumps(chunk.__dict__)

    def wrap_event(self, data):
        return "data: " + data + "\n\n"

    def get_packages(self):
        """
        合并相同package_id 的 chunk 到 packages
        """
        packages: List[MessagePackage] = []
        current_package_id = -1
        for chunk in self.chunks:
            if chunk.package_id == current_package_id:
                # 如果是同一个包，直接添加数据
                packages[-1].data += chunk.data
            else:
                # 如果不是同一个包，创建新的包

                package = MessagePackage(
                    package_id=chunk.package_id,
                    package_type=chunk.package_type,
                    status=MessagePackageStatus.FINISHED,
                    data=chunk.data if chunk.data else "",
                )

                current_package_id = chunk.package_id
                packages.append(package)

        return packages

    async def save_packages(self):
        """
        保存消息包到数据库
        """
        packages = self.get_packages()
        dict_packages = [package.model_dump() for package in packages]

        async with db_manager.session() as session:
            message_create = MessageCreate(
                id=self.message_id,
                agent_code=self.agent_code,
                conversation_id=self.conversation_id,
                message_type=MessageType.AI.value,
                content=dict_packages,
                data_object=self.data_object
            )
            # 保存到数据库
            await message_curd.create(db=session, obj_input=message_create)

        # 更新会话的 updated_at
        async with db_manager.session() as session:
            conversation = await conversation_curd.get_by_conversation_id(
                session, _id=self.conversation_id
            )
            if conversation:
                await conversation_curd.update(
                    db=session,
                    db_obj=conversation[0],
                    obj_input={"updated_at": datetime.now(timezone.utc)},
                )
