# 模型配置
ai:
  models:
    default: volcengine  # 默认模型标识符
    qwen:
      name: qwen-max
      api_key: your-qwen-api-key
      base_url: https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation

    ollama:
      name: llama3
      api_key: dummy-key
      base_url: http://localhost:11434/v1

    custom:
      name: aliyun/qwen-max
      api_key: sk-cjey9DW7GA2DY4idYdWtMVXPLHHLKuKtP1rcrlUX38QzBTrH
      base_url: http://***********:3000/v1

    volcengine:
      name: volcengine/deepseek-v3
      api_key: sk-giZY3EQGWzUhpuyslUfl5KWIv7wPh0nFul0k56wems7NnTXy
      base_url: http://***********:3000/v1  

# 用户校验接口配置
userUrl: http://************:8002/api/user/basic/current

  
# 大B端配置
business_system:
  host: ************
  port: 8002

# Redis 配置
redis:
  host: ************
  port: 6379
  password: span
  db: 0

# PostgreSQL 配置
database:
  url: postgresql+asyncpg://postgres:postgres@************:5432/agent_db

# Agent 配置
agents:
  - name: dynamic-page-creator
    impl: "core.api.dynamic_creator_sender.PageCreatorSender" 
    exclude_tools:  # 排除的工具列表
      - call_nl2sql_function

  - name: dynamic-page-qa
    impl: "core.api.dynamic_qa_sender.PageQaSender"

  - name: nl2sql-agent
    impl: "core.api.nl2sql_sender.NL2SqlSender"


mcp_servers:
  - key: dynamic-page-server
    type: stdio
    url: mcpserver/mcp_server.py
    enabled: true

  - key: other-server
    type: sse
    url: http://************:5602/sse/1
    enabled: false
    
gateway:
  services:
    - name: cluster
      url: http://************:8002/api

    - name: nl2sql
      url: http://***********:8000/api